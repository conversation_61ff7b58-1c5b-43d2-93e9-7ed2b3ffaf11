﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.Core.DTOs
{
    public class EmployeeWithParent
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public EmployeeRank Rank { get; set; }
        public long? CompanyId { get; set; }
        public bool IsOrderBooking { get; set; }
        public string Region { get; set; }
        public PortalUserRole UserRole { get; set; }
        public EmployeeWithParent Parent { get; set; }
        public string HQ { get; set; }
        public EmployeeType UserType { get; set; }
        public bool IsFieldAppUser { get; set; }
        public string ContactNo { get; set; }
        public string EmailId { get; set; }
        public bool IsVacant { get; set; }
        public string ClientSideId { get; set; }
    }
}
