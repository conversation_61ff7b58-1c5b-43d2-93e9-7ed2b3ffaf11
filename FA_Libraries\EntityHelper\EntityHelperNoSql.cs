﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;

namespace EntityHelper
{
    public interface IUpdatedEntityV2 : IEntityV2
    {
        DateTime LastUpdatedAt { get; set; }
    }

    public interface ICreatedEntityV2 : IEntityV2
    {
        DateTime CreatedAt { get; set; }
        string CreationContext { get; set; }
    }

    public interface IEntityV2
    {
        Guid Id { get; set; }
    }
}
