trigger:
  batch: true
  branches:
    include:
      - main

pool:
  name: "scaleset-agent"

variables:
  buildConfiguration: 'Release'

jobs:
- job: BuildAndAnalyze
  timeoutInMinutes: 60
  pool:
    name: "scaleset-agent"
  steps:
  - checkout: self
    persistCredentials: true
    clean: true
    submodules: recursive
    fetchDepth: 0


  - script: dotnet restore
    displayName: 'dotnet restore'

  - script: dotnet format --verify-no-changes --exclude ./fa_dotnet_clickhouse_connector ./fa_dotnet_core ./fa_dotnet_logger --verbosity d
    displayName: 'Check code formatting'

  # - task: SonarQubePrepare@7
  #   inputs:
  #     SonarQube: 'SonarQube'
  #     scannerMode: 'dotnet'
  #     projectKey: 'F2k.Libraries_F2k.Libraries_ef856762-b180-459d-aff6-d1d1f1d16879'
  #     projectName: 'F2k.Libraries'
  #     extraProperties: |
  #       sonar.scanner.skipJreProvisioning=true
  #       sonar.scanner.scanAll=false
  #   displayName: 'Prepare SonarQube Analysis'

  - script: dotnet build --configuration $(buildConfiguration)
    displayName: 'Build the solution'

  # - task: SonarQubeAnalyze@7
  #   inputs:
  #     jdkversion: 'JAVA_HOME'
  #   displayName: 'Run SonarQube Analysis'
   
  # - task: SonarQubePublish@7
  #   inputs:
  #     pollingTimeoutSec: '300'
  #   displayName: 'Publish SonarQube Results'