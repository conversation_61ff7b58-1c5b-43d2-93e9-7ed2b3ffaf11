// Copyright (c) FieldAssist. All Rights Reserved.

using System.Diagnostics;
using System.Text.Json;
using Library.Logs.Services;
using Library.SlackService.Services;

namespace Libraries.Abstractions.Processors
{
    /// <summary>
    /// An Opinionated abstraction of a triggered processor that provides file logging on slack and Slack notification capabilities using <see cref="DirectSlackLogHelper"/> and <see cref="FileLogger">
    /// </summary>
    public abstract class TriggeredProcessorBase<T>(FileLogger fileLogger, DirectSlackLogHelper slackLogHelper, string channelId, bool sendCompressedLogs = false)
    {
        #region Private
        private readonly DirectSlackLogHelper _slackLogHelper = slackLogHelper;
        private readonly string _channelId = channelId;
        private readonly bool _sendCompressedLogs = sendCompressedLogs;
        private readonly Guid _instanceGuid = Guid.NewGuid();
        private string InstanceId => $"{GetType().Name}_{_instanceGuid}";
        #endregion Private

        #region Protected
        protected readonly FileLogger _fileLogger = fileLogger;
        protected bool errorsOccurredSendAlerts = false;
        /// <summary>
        /// List Slack Ids of users to tag in the error message in format: <@U04ULQ48L93>
        /// </summary>
        protected virtual List<string> SlackIdsOfPeopleToAlert => new List<string>();
        protected virtual string LogHeader => $"[{InstanceId}]: ";
        protected abstract Task _Process(T? input = default);

        /// <summary>
        /// throws an argument exception if the arguments are invalid
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual void ValidateArguments(T? input = default)
        {
            return;
        }

        protected virtual string GetLogFileName()
        {
            return $"{InstanceId}.txt";
        }
        #endregion Protected

        public virtual async Task Process(T? input = default)
        {
            var sw = new Stopwatch();
            sw.Start();
            var logFile = new FileInfo($"{FileLogger.s_logsDirectory}{GetLogFileName()}");

            try
            {
                await _fileLogger.InitializeAsync(logFile.Name, append: false, $"[{GetType().Name}]: ");
                ValidateArguments(input);
                _fileLogger.WriteLine($"Starting processor! With Input: {JsonSerializer.Serialize(input)}");
                await _slackLogHelper.SendMessageToChannel(LogHeader + "\nStarting  processor!", _channelId);
                await _Process(input);
                if (errorsOccurredSendAlerts)
                {
                    await _slackLogHelper.SendMessageToChannel(LogHeader + $"\nFinished processing with errors. Check log File For Details.\nAlerting:{string.Join(",", SlackIdsOfPeopleToAlert)}", _channelId);
                }
            }
            catch (Exception ex)
            {
                _fileLogger.WriteLine($"Error while processing:\n{ex}");
                const int StrackTraceMaxLength = 2000;
                var stackTrace = ex.StackTrace != null && ex.StackTrace.Length > StrackTraceMaxLength
                    ? string.Concat("...\n", ex.StackTrace.AsSpan(ex.StackTrace.Length - 1 - StrackTraceMaxLength, StrackTraceMaxLength))
                    : ex.StackTrace;
                await _slackLogHelper.SendMessageToChannel(LogHeader + $"\nUnHandled Exception Occured!\nAlerting:{string.Join(",", SlackIdsOfPeopleToAlert)}"
                    + $"\n{ex.Message}\n```{stackTrace}```", _channelId);
            }
            finally
            {
                sw.Stop();
                var logMessage = $"Finished processing in {sw.Elapsed.TotalSeconds}.";
                _fileLogger.WriteLine(logMessage);
                _fileLogger.Dispose();
                await _slackLogHelper.SendFileToChannel(logFile, _channelId, compressFile: _sendCompressedLogs);
                await _slackLogHelper.SendMessageToChannel(LogHeader + logMessage, _channelId);
            }
        }
    }
}
